// JWT Authentication middleware
import { FastifyRequest, FastifyReply } from 'fastify';
import jwt from 'jsonwebtoken';
import { env } from '../../infrastructure/config/env.js';

export interface AuthenticatedUser {
  userId: string;
  email: string;
  role: string;
  userType: string;
}

declare module 'fastify' {
  interface FastifyRequest {
    user?: AuthenticatedUser;
  }
}

export async function verifyJWT(request: FastifyRequest, reply: FastifyReply) {
  try {
    console.log('🔐 Auth middleware called for:', request.method, request.url);

    const authHeader = request.headers.authorization;
    console.log('📋 Auth header:', authHeader ? `Bearer ${authHeader.substring(7, 20)}...` : 'Missing');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.log('❌ Missing or invalid Authorization header');
      return reply.status(401).send({ error: 'Unauthorized - Missing or invalid Authorization header' });
    }

    const token = authHeader.substring(7);
    console.log('🎫 Token length:', token.length);

    if (!env.JWT_SECRET) {
      console.log('❌ JWT_SECRET not configured');
      return reply.status(500).send({ error: 'Server configuration error' });
    }

    const decoded = jwt.verify(token, env.JWT_SECRET) as any;
    console.log('✅ Token decoded successfully for user:', decoded.email);

    // Add user info to request
    request.user = {
      userId: decoded.sub,
      email: decoded.email,
      role: decoded.role,
      userType: decoded.userType
    };

    console.log('👤 User authenticated:', request.user.email, 'Role:', request.user.role);
  } catch (error) {
    console.log('💥 JWT verification failed:', error instanceof Error ? error.message : error);
    return reply.status(401).send({ error: 'Invalid token', details: error instanceof Error ? error.message : 'Unknown error' });
  }
}
