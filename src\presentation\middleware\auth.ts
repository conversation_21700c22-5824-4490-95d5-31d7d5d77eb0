// JWT Authentication middleware
import { FastifyRequest, FastifyReply } from 'fastify';
import jwt from 'jsonwebtoken';
import { env } from '../../infrastructure/config/env.js';

export interface AuthenticatedUser {
  userId: string;
  email: string;
  role: string;
  userType: string;
}

declare module 'fastify' {
  interface FastifyRequest {
    user?: AuthenticatedUser;
  }
}

export async function verifyJWT(request: FastifyRequest, reply: FastifyReply) {
  try {
    const authHeader = request.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return reply.status(401).send({ error: 'Unauthorized' });
    }

    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, env.JWT_SECRET) as any;
    
    // Add user info to request
    request.user = {
      userId: decoded.sub,
      email: decoded.email,
      role: decoded.role,
      userType: decoded.userType
    };
  } catch (error) {
    return reply.status(401).send({ error: 'Invalid token' });
  }
}
