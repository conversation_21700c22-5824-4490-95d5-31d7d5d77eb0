// User Controller - HTTP endpoints for user operations
import { FastifyRequest, FastifyReply } from 'fastify';
import { UserService } from '../../application/services/User.Service.js';
import { IAuditService } from '../../application/interfaces/IServices.js';
import { CryptoPasswordHasher } from '../../application/services/Password.Hasher.js';
import {
  CreateUserDto,
  UpdateUserDto,
  ChangeStatusDto,
  UnlockAccountDto,
  ResetPasswordDto,
  VerifyEmailDto,
  UserSearchFilterDto
} from '../../application/dto/user.dto.js';
import { ValidationError } from '../../application/validation/DtoValidator.js';
import crypto from 'crypto';

// Simple audit service implementation
class SimpleAuditService implements IAuditService {
  constructor(private prisma: any) {}

  async logAsync(
    userId: string,
    actionType: string,
    tableName: string,
    recordId: string,
    oldValues?: string,
    newValues?: string,
    additionalInfo?: string
  ): Promise<void> {
    try {
      await this.prisma.auditLog.create({
        data: {
          AuditLogId: crypto.randomUUID(),
          UserId: userId,
          ActionType: actionType,
          TableName: tableName,
          RecordId: recordId,
          OldValues: oldValues,
          NewValues: newValues,
          AdditionalInfo: additionalInfo,
          Timestamp: new Date()
        }
      });
    } catch (error) {
      console.error('Failed to log audit:', error);
      // Don't throw - audit logging shouldn't break the main operation
    }
  }
}

export class UserController {
  private userService: UserService;

  constructor(prisma: any) {
    const passwordHasher = new CryptoPasswordHasher();
    const auditService = new SimpleAuditService(prisma);
    this.userService = new UserService(prisma, passwordHasher, auditService);
  }

  /**
   * Create a new user
   * POST /api/users
   */
  async createUser(request: FastifyRequest<{ Body: CreateUserDto }>, reply: FastifyReply) {
    try {
      console.log('👤 Creating user...');
      console.log('📝 Request data:', request.body);

      const userDto = await this.userService.createUser(request.body);
      console.log('✅ User created successfully:', userDto);

      reply.status(201).send({
        success: true,
        message: 'User created successfully',
        data: userDto
      });
    } catch (error) {
      console.error('💥 User creation error:', error);

      if (error instanceof ValidationError) {
        reply.status(400).send({
          success: false,
          message: 'Validation failed',
          errors: error.errors
        });
      } else {
        reply.status(500).send({
          success: false,
          message: error instanceof Error ? error.message : 'Failed to create user'
        });
      }
    }
  }

  /**
   * Update an existing user
   * PUT /api/users/:id
   */
  async updateUser(request: FastifyRequest<{ Body: Omit<UpdateUserDto, 'userId'>; Params: { id: string } }>, reply: FastifyReply) {
    try {
      const updateDto = { ...request.body, userId: request.params.id };
      const success = await this.userService.updateUserAsync(updateDto);

      if (!success) {
        reply.status(404).send({
          success: false,
          message: 'User not found'
        });
        return;
      }

      // Get updated user for response
      const updatedUser = await this.userService.getUserByIdAsync(request.params.id);
      if (!updatedUser) {
        reply.status(404).send({
          success: false,
          message: 'User not found after update'
        });
        return;
      }

      reply.send({
        success: true,
        message: 'User updated successfully',
        data: {
          userId: updatedUser.userId,
          email: updatedUser.email,
          firstName: updatedUser.firstName,
          lastName: updatedUser.lastName,
          userType: updatedUser.userType,
          accountStatus: updatedUser.accountStatus
        }
      });
    } catch (error) {
      if (error instanceof ValidationError) {
        reply.status(400).send({
          success: false,
          message: 'Validation failed',
          errors: error.errors
        });
      } else {
        reply.status(500).send({
          success: false,
          message: error instanceof Error ? error.message : 'Failed to update user'
        });
      }
    }
  }

  /**
   * Change user status
   * PATCH /api/users/:id/status
   */
  async changeUserStatus(request: FastifyRequest<{ Body: { newStatus: string }; Params: { id: string } }>, reply: FastifyReply) {
    try {
      const changeStatusDto: ChangeStatusDto = {
        userId: request.params.id,
        newStatus: request.body.newStatus
      };
      
      const userDto = await this.userService.changeUserStatus(changeStatusDto);
      
      reply.send({
        success: true,
        message: 'User status changed successfully',
        data: userDto
      });
    } catch (error) {
      if (error instanceof ValidationError) {
        reply.status(400).send({
          success: false,
          message: 'Validation failed',
          errors: error.errors
        });
      } else {
        reply.status(500).send({
          success: false,
          message: error instanceof Error ? error.message : 'Failed to change user status'
        });
      }
    }
  }

  /**
   * Unlock user account
   * PATCH /api/users/:id/unlock
   */
  async unlockAccount(request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) {
    try {
      const unlockDto: UnlockAccountDto = {
        userId: request.params.id
      };
      
      const userDto = await this.userService.unlockAccount(unlockDto);
      
      reply.send({
        success: true,
        message: 'Account unlocked successfully',
        data: userDto
      });
    } catch (error) {
      if (error instanceof ValidationError) {
        reply.status(400).send({
          success: false,
          message: 'Validation failed',
          errors: error.errors
        });
      } else {
        reply.status(500).send({
          success: false,
          message: error instanceof Error ? error.message : 'Failed to unlock account'
        });
      }
    }
  }

  /**
   * Get user by ID
   * GET /api/users/:id
   */
  async getUserById(request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) {
    try {
      const user = await this.userService.getUserByIdAsync(request.params.id);

      if (!user) {
        reply.status(404).send({
          success: false,
          message: 'User not found'
        });
        return;
      }

      reply.send({
        success: true,
        message: 'User retrieved successfully',
        data: {
          userId: user.userId,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          userType: user.userType,
          accountStatus: user.accountStatus,
          userCreatedAt: user.userCreatedAt,
          userModifiedAt: user.userModifiedAt,
          isAccountLockedOut: user.isAccountLockedOut,
          failedLoginAttempts: user.failedLoginAttempts,
          userProfilePicPath: user.userProfilePicPath
        }
      });
    } catch (error) {
      reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get user'
      });
    }
  }

  /**
   * Get all users with advanced filtering, searching, and pagination
   * GET /api/users
   */
  async getUsers(request: FastifyRequest<{
    Querystring: {
      page?: string;
      limit?: string;
      searchTerm?: string;
      roleFilter?: string;
      statusFilter?: string;
      departmentFilter?: string;
      userTypeFilter?: string;
      sortBy?: string;
      sortOrder?: string;
    }
  }>, reply: FastifyReply) {
    try {
      console.log('🎯 getUsers endpoint called');
      console.log('📝 Query parameters:', request.query);

      // Parse query parameters
      const filters: UserSearchFilterDto = {
        page: parseInt(request.query.page || '1'),
        limit: parseInt(request.query.limit || '10'),
        searchTerm: request.query.searchTerm,
        roleFilter: request.query.roleFilter,
        statusFilter: request.query.statusFilter,
        departmentFilter: request.query.departmentFilter,
        userTypeFilter: request.query.userTypeFilter as "TSLS" | "University" | undefined,
        sortBy: (request.query.sortBy as any) || 'userCreatedAt',
        sortOrder: (request.query.sortOrder as 'asc' | 'desc') || 'desc'
      };

      console.log('🔧 Parsed filters:', filters);

      // Check if any filters are applied
      const hasFilters = filters.searchTerm || filters.roleFilter || filters.statusFilter ||
                        filters.departmentFilter || filters.userTypeFilter;

      console.log('🔍 Has filters:', hasFilters);

      let result;
      // Always try the simple method first to debug
      console.log('📋 Testing getAllUsersAsync first...');
      try {
        const allUsers = await this.userService.getAllUsersAsync();
        console.log('📊 getAllUsersAsync returned', allUsers.length, 'users');

        if (allUsers.length === 0) {
          console.log('❌ No users found in database!');
          reply.send({
            success: true,
            message: 'No users found in database',
            data: {
              users: [],
              pagination: {
                page: 1,
                limit: 10,
                total: 0,
                totalPages: 0,
                hasNext: false,
                hasPrev: false
              },
              filters: {}
            }
          });
          return;
        }

        if (!hasFilters) {
          // No filters, use simple pagination
          const startIndex = (filters.page - 1) * filters.limit;
          const endIndex = startIndex + filters.limit;
          const paginatedUsers = allUsers.slice(startIndex, endIndex);

          result = {
            users: paginatedUsers,
            pagination: {
              page: filters.page,
              limit: filters.limit,
              total: allUsers.length,
              totalPages: Math.ceil(allUsers.length / filters.limit),
              hasNext: endIndex < allUsers.length,
              hasPrev: filters.page > 1
            },
            filters: {}
          };
        } else {
          console.log('🔍 Using getUsersWithFiltersAsync...');
          result = await this.userService.getUsersWithFiltersAsync(filters);
        }
      } catch (simpleError) {
        console.error('💥 Error with getAllUsersAsync:', simpleError);
        console.log('🔍 Falling back to getUsersWithFiltersAsync...');
        result = await this.userService.getUsersWithFiltersAsync(filters);
      }

      console.log('✅ Service returned result with', result.users.length, 'users');

      reply.send({
        success: true,
        message: 'Users retrieved successfully',
        data: result
      });
    } catch (error) {
      console.error('💥 Get users error:', error);
      reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get users'
      });
    }
  }

  /**
   * Get user statistics
   * GET /api/users/stats
   */
  async getUserStats(request: FastifyRequest, reply: FastifyReply) {
    try {
      const stats = await this.userService.getUserStatsAsync();

      reply.send({
        success: true,
        message: 'User statistics retrieved successfully',
        data: stats
      });
    } catch (error) {
      console.error('💥 Get user stats error:', error);
      reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get user statistics'
      });
    }
  }

  /**
   * Delete user
   * DELETE /api/users/:id
   */
  async deleteUser(request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) {
    try {
      const success = await this.userService.deleteUserAsync(request.params.id);

      if (!success) {
        reply.status(404).send({
          success: false,
          message: 'User not found'
        });
        return;
      }

      reply.send({
        success: true,
        message: 'User deleted successfully'
      });
    } catch (error) {
      reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to delete user'
      });
    }
  }

  /**
   * Reset password
   * POST /api/users/reset-password
   */
  async resetPassword(request: FastifyRequest<{ Body: ResetPasswordDto }>, reply: FastifyReply) {
    try {
      const success = await this.userService.resetPasswordAsync(request.body);

      if (!success) {
        reply.status(400).send({
          success: false,
          message: 'Invalid or expired reset token'
        });
        return;
      }

      reply.send({
        success: true,
        message: 'Password reset successfully'
      });
    } catch (error) {
      if (error instanceof ValidationError) {
        reply.status(400).send({
          success: false,
          message: 'Validation failed',
          errors: error.errors
        });
      } else {
        reply.status(500).send({
          success: false,
          message: error instanceof Error ? error.message : 'Failed to reset password'
        });
      }
    }
  }

  /**
   * Verify email
   * POST /api/users/verify-email
   */
  async verifyEmail(request: FastifyRequest<{ Body: VerifyEmailDto }>, reply: FastifyReply) {
    try {
      const success = await this.userService.verifyEmailAsync(request.body);

      if (!success) {
        reply.status(400).send({
          success: false,
          message: 'Invalid verification token or user not found'
        });
        return;
      }

      reply.send({
        success: true,
        message: 'Email verified successfully'
      });
    } catch (error) {
      if (error instanceof ValidationError) {
        reply.status(400).send({
          success: false,
          message: 'Validation failed',
          errors: error.errors
        });
      } else {
        reply.status(500).send({
          success: false,
          message: error instanceof Error ? error.message : 'Failed to verify email'
        });
      }
    }
  }

  /**
   * Get all roles for dropdown/filter options
   * GET /api/users/roles
   */
  async getRoles(request: FastifyRequest, reply: FastifyReply) {
    try {
      const roles = await this.userService.getAllRolesAsync();

      reply.send({
        success: true,
        message: 'Roles retrieved successfully',
        data: roles
      });
    } catch (error) {
      console.error('💥 Get roles error:', error);
      reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get roles'
      });
    }
  }

  /**
   * Get all departments for dropdown/filter options
   * GET /api/users/departments
   */
  async getDepartments(request: FastifyRequest, reply: FastifyReply) {
    try {
      const departments = await this.userService.getAllDepartmentsAsync();

      reply.send({
        success: true,
        message: 'Departments retrieved successfully',
        data: departments
      });
    } catch (error) {
      console.error('💥 Get departments error:', error);
      reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to get departments'
      });
    }
  }

  /**
   * Get user status options
   * GET /api/users/status-options
   */
  async getStatusOptions(request: FastifyRequest, reply: FastifyReply) {
    try {
      const statusOptions = ['Active', 'Inactive', 'Pending', 'Suspended'];

      reply.send({
        success: true,
        message: 'Status options retrieved successfully',
        data: statusOptions
      });
    } catch (error) {
      reply.status(500).send({
        success: false,
        message: 'Failed to get status options'
      });
    }
  }

  /**
   * Get user type options
   * GET /api/users/user-types
   */
  async getUserTypes(request: FastifyRequest, reply: FastifyReply) {
    try {
      const userTypes = ['TSLS', 'University'];

      reply.send({
        success: true,
        message: 'User types retrieved successfully',
        data: userTypes
      });
    } catch (error) {
      reply.status(500).send({
        success: false,
        message: 'Failed to get user types'
      });
    }
  }

  /**
   * Debug endpoint to test basic user retrieval
   * GET /api/users/debug
   */
  async debugUsers(request: FastifyRequest, reply: FastifyReply) {
    try {
      console.log('🐛 Debug endpoint called');

      // Test basic getAllUsersAsync
      const allUsers = await this.userService.getAllUsersAsync();
      console.log('📊 getAllUsersAsync returned:', allUsers.length, 'users');

      if (allUsers.length > 0) {
        console.log('👤 First user sample:', {
          userId: allUsers[0].userId,
          email: allUsers[0].email,
          firstName: allUsers[0].firstName,
          lastName: allUsers[0].lastName,
          roleName: allUsers[0].roleName
        });
      }

      reply.send({
        success: true,
        message: 'Debug info retrieved',
        data: {
          totalUsers: allUsers.length,
          sampleUser: allUsers.length > 0 ? allUsers[0] : null,
          allUsers: allUsers.slice(0, 3) // Return first 3 users for debugging
        }
      });
    } catch (error) {
      console.error('💥 Debug error:', error);
      reply.status(500).send({
        success: false,
        message: error instanceof Error ? error.message : 'Debug failed'
      });
    }
  }
}
