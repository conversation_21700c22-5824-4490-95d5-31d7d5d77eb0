// User routes
import { FastifyInstance } from 'fastify';
import { UserController } from '../controllers/User.Controller.js';
import { verifyJWT } from '../middleware/auth.js';
import {
  CreateUserDto,
  UpdateUserDto,
  ChangeStatusDto,
  ResetPasswordDto,
  VerifyEmailDto,
  UserDto,
  CreateUserSchema,
  UpdateUserSchema,
  ChangeStatusSchema,
  ResetPasswordSchema,
  VerifyEmailSchema
} from '../../application/dto/user.dto.js';

export default async function userRoutes(fastify: FastifyInstance) {
  const userController = new UserController(fastify.prisma);

  // Test endpoint without authentication
  fastify.get('/users/test', {
    schema: {
      description: 'Test endpoint without authentication',
      tags: ['Users'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            timestamp: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    reply.send({
      success: true,
      message: 'Test endpoint working - no auth required',
      timestamp: new Date().toISOString()
    });
  });

  // Swagger schemas for documentation
  const userResponseSchema = {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      message: { type: 'string' },
      data: {
        type: 'object',
        properties: {
          userId: { type: 'string' },
          email: { type: 'string' },
          firstName: { type: 'string' },
          lastName: { type: 'string' },
          roleId: { type: 'string' },
          roleName: { type: 'string' },
          departmentId: { type: 'string' },
          departmentName: { type: 'string' },
          userType: { type: 'string' },
          accountStatus: { type: 'string' },
          userCreatedAt: { type: 'string', format: 'date-time' },
          userModifiedAt: { type: 'string', format: 'date-time' },
          isAccountLockedOut: { type: 'boolean' },
          failedLoginAttempts: { type: 'number' },
          userProfilePicPath: { type: 'string' }
        }
      }
    }
  };

  const errorResponseSchema = {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      message: { type: 'string' },
      errors: {
        type: 'array',
        items: { type: 'string' }
      }
    }
  };

  // Create user
  fastify.post<{ Body: CreateUserDto }>('/users', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Create a new user',
      tags: ['Users'],
      security: [{ bearerAuth: [] }],
      response: {
        201: userResponseSchema,
        400: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, userController.createUser.bind(userController));

  // Update user
  fastify.put<{ Body: Omit<UpdateUserDto, 'userId'>; Params: { id: string } }>('/users/:id', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Update an existing user',
      tags: ['Users'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      body: {
        type: 'object',
        properties: {
          firstName: { type: 'string', minLength: 1 },
          lastName: { type: 'string', minLength: 1 },
          departmentId: { type: 'string', format: 'uuid' },
          roleId: { type: 'string', format: 'uuid' }
        }
      },
      response: {
        200: userResponseSchema,
        400: errorResponseSchema,
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, userController.updateUser.bind(userController));

  // Change user status
  fastify.patch<{ Body: { newStatus: string }; Params: { id: string } }>('/users/:id/status', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Change user status',
      tags: ['Users'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      body: {
        type: 'object',
        properties: {
          newStatus: { type: 'string', minLength: 1 }
        },
        required: ['newStatus']
      },
      response: {
        200: userResponseSchema,
        400: errorResponseSchema,
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, userController.changeUserStatus.bind(userController));

  // Unlock user account
  fastify.patch<{ Params: { id: string } }>('/users/:id/unlock', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Unlock user account',
      tags: ['Users'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      response: {
        200: userResponseSchema,
        400: errorResponseSchema,
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, userController.unlockAccount.bind(userController));

  // Get all users with advanced filtering
  fastify.get<{
    Querystring: {
      page?: string;
      limit?: string;
      searchTerm?: string;
      roleFilter?: string;
      statusFilter?: string;
      departmentFilter?: string;
      userTypeFilter?: string;
      sortBy?: string;
      sortOrder?: string;
    }
  }>('/users', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Get all users with advanced filtering, searching, and pagination',
      tags: ['Users'],
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'string', pattern: '^[1-9]\\d*$' },
          limit: { type: 'string', pattern: '^[1-9]\\d*$' },
          searchTerm: { type: 'string' },
          roleFilter: { type: 'string' },
          statusFilter: { type: 'string' },
          departmentFilter: { type: 'string' },
          userTypeFilter: { type: 'string', enum: ['TSLS', 'University'] },
          sortBy: { type: 'string', enum: ['firstName', 'lastName', 'email', 'userCreatedAt', 'accountStatus'] },
          sortOrder: { type: 'string', enum: ['asc', 'desc'] }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            data: {
              type: 'object',
              properties: {
                users: {
                  type: 'array',
                  items: userResponseSchema.properties.data
                },
                pagination: {
                  type: 'object',
                  properties: {
                    page: { type: 'number' },
                    limit: { type: 'number' },
                    total: { type: 'number' },
                    totalPages: { type: 'number' },
                    hasNext: { type: 'boolean' },
                    hasPrev: { type: 'boolean' }
                  }
                },
                filters: {
                  type: 'object',
                  properties: {
                    searchTerm: { type: 'string' },
                    roleFilter: { type: 'string' },
                    statusFilter: { type: 'string' },
                    departmentFilter: { type: 'string' },
                    userTypeFilter: { type: 'string' }
                  }
                }
              }
            }
          }
        },
        500: errorResponseSchema
      }
    }
  }, userController.getUsers.bind(userController));

  // Get user by ID
  fastify.get<{ Params: { id: string } }>('/users/:id', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Get user by ID',
      tags: ['Users'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      response: {
        200: userResponseSchema,
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, userController.getUserById.bind(userController));

  // Delete user
  fastify.delete<{ Params: { id: string } }>('/users/:id', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Delete user',
      tags: ['Users'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        },
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, userController.deleteUser.bind(userController));

  // Reset password
  fastify.post<{ Body: ResetPasswordDto }>('/users/reset-password', {
    schema: {
      description: 'Reset user password',
      tags: ['Users'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        },
        400: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, userController.resetPassword.bind(userController));

  // Verify email
  fastify.post<{ Body: VerifyEmailDto }>('/users/verify-email', {
    schema: {
      description: 'Verify user email',
      tags: ['Users'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        },
        400: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, userController.verifyEmail.bind(userController));

  // Get user statistics
  fastify.get('/users/stats', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Get user statistics',
      tags: ['Users'],
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            data: {
              type: 'object',
              properties: {
                totalUsers: { type: 'number' },
                activeUsers: { type: 'number' },
                inactiveUsers: { type: 'number' },
                lockedUsers: { type: 'number' },
                usersByRole: { type: 'object' },
                usersByDepartment: { type: 'object' },
                usersByType: { type: 'object' },
                recentUsers: { type: 'number' }
              }
            }
          }
        },
        500: errorResponseSchema
      }
    }
  }, userController.getUserStats.bind(userController));

  // Get all roles
  fastify.get('/users/roles', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Get all active roles',
      tags: ['Users'],
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  roleId: { type: 'string' },
                  roleName: { type: 'string' },
                  description: { type: 'string' }
                }
              }
            }
          }
        },
        500: errorResponseSchema
      }
    }
  }, userController.getRoles.bind(userController));

  // Get all departments
  fastify.get('/users/departments', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Get all active departments',
      tags: ['Users'],
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  departmentId: { type: 'string' },
                  departmentName: { type: 'string' },
                  description: { type: 'string' }
                }
              }
            }
          }
        },
        500: errorResponseSchema
      }
    }
  }, userController.getDepartments.bind(userController));

  // Get status options
  fastify.get('/users/status-options', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Get available user status options',
      tags: ['Users'],
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            data: {
              type: 'array',
              items: { type: 'string' }
            }
          }
        },
        500: errorResponseSchema
      }
    }
  }, userController.getStatusOptions.bind(userController));

  // Get user types
  fastify.get('/users/user-types', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Get available user types',
      tags: ['Users'],
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            data: {
              type: 'array',
              items: { type: 'string' }
            }
          }
        },
        500: errorResponseSchema
      }
    }
  }, userController.getUserTypes.bind(userController));

  // Debug endpoint
  fastify.get('/users/debug', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Debug endpoint for testing user retrieval',
      tags: ['Users'],
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            data: { type: 'object' }
          }
        },
        500: errorResponseSchema
      }
    }
  }, userController.debugUsers.bind(userController));
}
