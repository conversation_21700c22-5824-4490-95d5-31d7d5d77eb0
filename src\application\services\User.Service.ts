// User Service - Business logic for user operations
import { PrismaClient } from '../../generated/prisma/index.js';
import { PTEIUser, UserType } from '../../domain/index.entity.js';
// Import DTOs and Zod schemas
import {
  CreateUserDto,
  UpdateUserDto,
  ChangeStatusDto,
  UnlockAccountDto,
  ResetPasswordDto,
  VerifyEmailDto,
  UserDto,
  CreateUserSchema,
  UpdateUserSchema,
  ChangeStatusSchema,
  UnlockAccountSchema,
  ResetPasswordSchema,
  VerifyEmailSchema,
  UnlockAccountDtoSchema,
  UserSearchFilterDto,
  UserSearchFilterSchema,
  PaginatedUsersResponseDto,
  UserStatsDto
} from '../dto/user.dto.js';
import { IAuditService, IPasswordHasher, IUserService } from '../interfaces/IServices.js';
import { DtoValidator } from '../validation/DtoValidator.js';
import { isValidEmail, isValidTIN, isStrongPassword } from '../../helpers/helperFunctions.js';

// Helper function to validate UUID format
function isValidUuid(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

// Helper function to convert database entity to domain object
function toDomainUser(dbUser: any): PTEIUser {
  return new PTEIUser(
    dbUser.UserId,
    dbUser.Email,
    dbUser.HashedPassword,
    dbUser.FirstName || '',
    dbUser.LastName || '',
    dbUser.UserProfilePicPath || '',
    dbUser.UserType as UserType,
    dbUser.AccountStatus || '',
    dbUser.IsAccountLockedOut || false,
    dbUser.FailedLoginAttempts || 0,
    dbUser.IsUserLoggedIn || false,
    dbUser.UserCreatedAt || new Date(),
    dbUser.UserModifiedAt,
    dbUser.EmailVerificationToken,
    dbUser.EmailVerificationTokenExpiry,
    dbUser.PasswordResetToken,
    dbUser.PasswordResetTokenExpiry,
    dbUser.RoleId,
    dbUser.DepartmentId,
    dbUser.TIN,
    dbUser.OtherName,
    dbUser.PhoneContact,
    dbUser.Title,
    dbUser.Gender,
    dbUser.Ethnicity,
    dbUser.DateOfBirth,
    dbUser.ResidentialAddress,
    dbUser.PostalAddress,
    dbUser.Province,
    dbUser.TSLSScheme
  );
}
import crypto from 'crypto';

export interface IUniversityRepository {
  getByIdAsync(universityId: string): Promise<any>;
}

export class UserService implements IUserService {
  constructor(
    private prisma: PrismaClient,
    private passwordHasher: IPasswordHasher,
    private auditService: IAuditService,
    private universityRepository?: IUniversityRepository
  ) {}

  /**
   * Creates a new user (equivalent to CreateUserAsync in C#)
   */
  async createUserAsync(createUserDto: CreateUserDto): Promise<PTEIUser> {
    // Validate DTO using Zod
    const validatedDto = CreateUserSchema.parse(createUserDto);

    // Check if user already exists
    const existingUser = await this.getUserByEmailAsync(validatedDto.email);
    if (existingUser) {
      throw new Error('User with this email already exists.');
    }

    // Validate university for University users
    if (validatedDto.userType === 'University' && !validatedDto.universityId) {
      throw new Error('UniversityId is required for university users.');
    }

    if (validatedDto.userType === 'University' && validatedDto.universityId) {
      const university = await this.prisma.university.findUnique({
        where: { UniversityId: validatedDto.universityId }
      });
      if (!university) {
        throw new Error('Invalid UniversityId.');
      }
    }

    // Hash password
    const hashedPassword = this.passwordHasher.hash(validatedDto.password);

    // Get profile picture path
    const profilePicPath = this.getProfilePicPath(
      validatedDto.userType,
      validatedDto.roleId,
      validatedDto.departmentId
    );

    // Create user
    const userId = crypto.randomUUID();
    const user = await this.prisma.pTEIUser.create({
      data: {
        UserId: userId,
        email: validatedDto.email,
        HashedPassword: hashedPassword,
        FirstName: validatedDto.firstName,
        LastName: validatedDto.lastName,
        DepartmentId: validatedDto.departmentId,
        RoleId: validatedDto.roleId,
        UserType: validatedDto.userType,
        AccountStatus: 'Pending',
        UserCreatedAt: new Date(),
        UserProfilePicPath: profilePicPath,
        IsAccountLockedOut: false,
        FailedLoginAttempts: 0,
        IsUserLoggedIn: false
      }
    });

    // Create university user relationship if needed
    if (validatedDto.userType === 'University' && validatedDto.universityId) {
      await this.prisma.universityUser.create({
        data: {
          UniversityUserId: crypto.randomUUID(),
          UserId: userId,
          UniversityId: validatedDto.universityId,
          CreatedAt: new Date(),
          IsActive: true
        }
      });
    }

    // Log audit
    await this.auditService.logAsync(
      userId,
      'CREATE_USER',
      'PTEIUser',
      userId,
      undefined,
      JSON.stringify(user),
      'User created'
    );

    return toDomainUser(user);
  }

  /**
   * Gets a user by email (equivalent to GetUserByEmailAsync in C#)
   */
  async getUserByEmailAsync(email: string): Promise<PTEIUser | null> {
    const user = await this.prisma.pTEIUser.findUnique({
      where: { email: email }
    });

    return user ? toDomainUser(user) : null;
  }

  /**
   * Gets a user by ID (equivalent to GetUserByIdAsync in C#)
   */
  async getUserByIdAsync(userId: string): Promise<PTEIUser | null> {
    if (!isValidUuid(userId)) {
      throw new Error('Invalid user ID format');
    }

    const user = await this.prisma.pTEIUser.findUnique({
      where: { UserId: userId }
    });

    return user ? toDomainUser(user) : null;
  }

  /**
   * Gets all users (equivalent to GetAllUsersAsync in C#)
   */
  async getAllUsersAsync(): Promise<UserDto[]> {
    const users = await this.prisma.pTEIUser.findMany({
      include: {
        Role: true,
        Department: true
      }
    });

    return users.map(user => this.mapToUserDto(user));
  }

  /**
   * Gets users with advanced filtering, searching, and pagination
   */
  async getUsersWithFiltersAsync(filters: UserSearchFilterDto): Promise<PaginatedUsersResponseDto> {
    try {
      console.log('🔍 getUsersWithFiltersAsync called with filters:', filters);

      // Validate filters using Zod
      const validatedFilters = UserSearchFilterSchema.parse(filters);
      console.log('✅ Validated filters:', validatedFilters);

      // Build where clause for filtering
      const whereClause: any = {};

      // Search term filter (searches across firstName, lastName, email)
      if (validatedFilters.searchTerm) {
        whereClause.OR = [
          { FirstName: { contains: validatedFilters.searchTerm, mode: 'insensitive' } },
          { LastName: { contains: validatedFilters.searchTerm, mode: 'insensitive' } },
          { email: { contains: validatedFilters.searchTerm, mode: 'insensitive' } }
        ];
        console.log('🔍 Added search filter for:', validatedFilters.searchTerm);
      }

      // Role filter
      if (validatedFilters.roleFilter) {
        whereClause.Role = {
          RoleName: validatedFilters.roleFilter
        };
        console.log('👤 Added role filter for:', validatedFilters.roleFilter);
      }

      // Status filter
      if (validatedFilters.statusFilter) {
        whereClause.AccountStatus = validatedFilters.statusFilter;
        console.log('📊 Added status filter for:', validatedFilters.statusFilter);
      }

      // Department filter
      if (validatedFilters.departmentFilter) {
        whereClause.Department = {
          DepartmentName: validatedFilters.departmentFilter
        };
        console.log('🏢 Added department filter for:', validatedFilters.departmentFilter);
      }

      // User type filter
      if (validatedFilters.userTypeFilter) {
        whereClause.UserType = validatedFilters.userTypeFilter;
        console.log('🏷️ Added user type filter for:', validatedFilters.userTypeFilter);
      }

      console.log('🔧 Final where clause:', JSON.stringify(whereClause, null, 2));

      // Build orderBy clause
      const orderByClause: any = {};
      switch (validatedFilters.sortBy) {
        case 'firstName':
          orderByClause.FirstName = validatedFilters.sortOrder;
          break;
        case 'lastName':
          orderByClause.LastName = validatedFilters.sortOrder;
          break;
        case 'email':
          orderByClause.email = validatedFilters.sortOrder;
          break;
        case 'accountStatus':
          orderByClause.AccountStatus = validatedFilters.sortOrder;
          break;
        case 'userCreatedAt':
        default:
          orderByClause.UserCreatedAt = validatedFilters.sortOrder;
          break;
      }

      // Calculate pagination
      const skip = (validatedFilters.page - 1) * validatedFilters.limit;
      console.log('📄 Pagination - skip:', skip, 'take:', validatedFilters.limit);

      // Get total count for pagination
      const totalCount = await this.prisma.pTEIUser.count({
        where: whereClause
      });
      console.log('📊 Total count:', totalCount);

      // Get filtered and paginated users
      const users = await this.prisma.pTEIUser.findMany({
        where: whereClause,
        include: {
          Role: true,
          Department: true
        },
        orderBy: orderByClause,
        skip: skip,
        take: validatedFilters.limit
      });
      console.log('👥 Found users:', users.length);

      // Map to DTOs
      const userDtos = users.map(user => this.mapToUserDto(user));

      // Calculate pagination info
      const totalPages = Math.ceil(totalCount / validatedFilters.limit);
      const hasNext = validatedFilters.page < totalPages;
      const hasPrev = validatedFilters.page > 1;

      const result = {
        users: userDtos,
        pagination: {
          page: validatedFilters.page,
          limit: validatedFilters.limit,
          total: totalCount,
          totalPages,
          hasNext,
          hasPrev
        },
        filters: {
          searchTerm: validatedFilters.searchTerm,
          roleFilter: validatedFilters.roleFilter,
          statusFilter: validatedFilters.statusFilter,
          departmentFilter: validatedFilters.departmentFilter,
          userTypeFilter: validatedFilters.userTypeFilter
        }
      };

      console.log('✅ Returning result with', userDtos.length, 'users');
      return result;

    } catch (error) {
      console.error('💥 Error in getUsersWithFiltersAsync:', error);
      throw error;
    }
  }

  /**
   * Gets user statistics for dashboard/analytics
   */
  async getUserStatsAsync(): Promise<UserStatsDto> {
    // Get total counts
    const totalUsers = await this.prisma.pTEIUser.count();
    const activeUsers = await this.prisma.pTEIUser.count({
      where: { AccountStatus: 'Active' }
    });
    const inactiveUsers = await this.prisma.pTEIUser.count({
      where: { AccountStatus: 'Inactive' }
    });
    const lockedUsers = await this.prisma.pTEIUser.count({
      where: { IsAccountLockedOut: true }
    });

    // Get users by role
    const roleStats = await this.prisma.pTEIUser.groupBy({
      by: ['RoleId'],
      _count: true,
      where: { RoleId: { not: null } }
    });

    const roleStatsWithNames: Record<string, number> = {};
    for (const stat of roleStats) {
      const role = await this.prisma.role.findUnique({
        where: { RoleId: stat.RoleId! }
      });
      if (role) {
        roleStatsWithNames[role.RoleName] = stat._count;
      }
    }

    // Get users by department
    const deptStats = await this.prisma.pTEIUser.groupBy({
      by: ['DepartmentId'],
      _count: true,
      where: { DepartmentId: { not: null } }
    });

    const deptStatsWithNames: Record<string, number> = {};
    for (const stat of deptStats) {
      const dept = await this.prisma.department.findUnique({
        where: { DepartmentId: stat.DepartmentId! }
      });
      if (dept) {
        deptStatsWithNames[dept.DepartmentName] = stat._count;
      }
    }

    // Get users by type
    const typeStats = await this.prisma.pTEIUser.groupBy({
      by: ['UserType'],
      _count: true,
      where: { UserType: { not: null } }
    });

    const typeStatsRecord: Record<string, number> = {};
    typeStats.forEach(stat => {
      if (stat.UserType) {
        typeStatsRecord[stat.UserType] = stat._count;
      }
    });

    // Get recent users (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentUsers = await this.prisma.pTEIUser.count({
      where: {
        UserCreatedAt: {
          gte: thirtyDaysAgo
        }
      }
    });

    return {
      totalUsers,
      activeUsers,
      inactiveUsers,
      lockedUsers,
      usersByRole: roleStatsWithNames,
      usersByDepartment: deptStatsWithNames,
      usersByType: typeStatsRecord,
      recentUsers
    };
  }

  /**
   * Gets all active roles for dropdown/filter options
   */
  async getAllRolesAsync(): Promise<Array<{ roleId: string; roleName: string; description?: string }>> {
    const roles = await this.prisma.role.findMany({
      where: { IsActive: true },
      orderBy: { RoleName: 'asc' }
    });

    return roles.map(role => ({
      roleId: role.RoleId,
      roleName: role.RoleName,
      description: role.Description || undefined
    }));
  }

  /**
   * Gets all active departments for dropdown/filter options
   */
  async getAllDepartmentsAsync(): Promise<Array<{ departmentId: string; departmentName: string; description?: string }>> {
    const departments = await this.prisma.department.findMany({
      where: { IsActive: true },
      orderBy: { DepartmentName: 'asc' }
    });

    return departments.map(dept => ({
      departmentId: dept.DepartmentId,
      departmentName: dept.DepartmentName,
      description: dept.Description || undefined
    }));
  }

  /**
   * Updates a user (equivalent to UpdateUserAsync in C#)
   */
  async updateUserAsync(updateUserDto: UpdateUserDto): Promise<boolean> {
    // Validate DTO using Zod
    const validatedDto = UpdateUserSchema.parse(updateUserDto);

    const user = await this.prisma.pTEIUser.findUnique({
      where: { UserId: validatedDto.userId }
    });

    if (!user) {
      return false;
    }

    const updatedUser = await this.prisma.pTEIUser.update({
      where: { UserId: validatedDto.userId },
      data: {
        FirstName: validatedDto.firstName || user.FirstName,
        LastName: validatedDto.lastName || user.LastName,
        DepartmentId: validatedDto.departmentId || user.DepartmentId,
        RoleId: validatedDto.roleId || user.RoleId,
        UserModifiedAt: new Date()
      }
    });

    // Log audit
    await this.auditService.logAsync(
      validatedDto.userId,
      'UPDATE_USER',
      'PTEIUser',
      validatedDto.userId,
      JSON.stringify(user),
      JSON.stringify(updatedUser),
      'User updated'
    );

    return true;
  }

  /**
   * Deletes a user (equivalent to DeleteUserAsync in C#)
   */
  async deleteUserAsync(userId: string): Promise<boolean> {
    if (!isValidUuid(userId)) {
      throw new Error('Invalid user ID format');
    }

    try {
      const user = await this.prisma.pTEIUser.findUnique({
        where: { UserId: userId }
      });

      if (!user) {
        return false;
      }

      await this.prisma.pTEIUser.delete({
        where: { UserId: userId }
      });

      // Log audit
      await this.auditService.logAsync(
        userId,
        'DELETE_USER',
        'PTEIUser',
        userId,
        JSON.stringify(user),
        undefined,
        'User deleted'
      );

      return true;
    } catch (error) {
      console.error('Error deleting user:', error);
      return false;
    }
  }

  /**
   * Changes user department (equivalent to ChangeUserDepartmentAsync in C#)
   */
  async changeUserDepartmentAsync(userId: string, departmentId: string): Promise<boolean> {
    if (!DtoValidator.isValidUuid(userId) || !DtoValidator.isValidUuid(departmentId)) {
      throw new Error('Invalid user ID or department ID format');
    }

    try {
      const user = await this.prisma.pTEIUser.findUnique({
        where: { UserId: userId }
      });

      if (!user) {
        return false;
      }

      await this.prisma.pTEIUser.update({
        where: { UserId: userId },
        data: {
          DepartmentId: departmentId,
          UserModifiedAt: new Date()
        }
      });

      // Log audit
      await this.auditService.logAsync(
        userId,
        'CHANGE_DEPARTMENT',
        'PTEIUser',
        userId,
        user.DepartmentId || '',
        departmentId,
        'User department changed'
      );

      return true;
    } catch (error) {
      console.error('Error changing user department:', error);
      return false;
    }
  }

  /**
   * Changes user role (equivalent to ChangeUserRoleAsync in C#)
   */
  async changeUserRoleAsync(userId: string, roleId: string): Promise<boolean> {
    if (!DtoValidator.isValidUuid(userId) || !DtoValidator.isValidUuid(roleId)) {
      throw new Error('Invalid user ID or role ID format');
    }

    try {
      const user = await this.prisma.pTEIUser.findUnique({
        where: { UserId: userId }
      });

      if (!user) {
        return false;
      }

      await this.prisma.pTEIUser.update({
        where: { UserId: userId },
        data: {
          RoleId: roleId,
          UserModifiedAt: new Date()
        }
      });

      // Log audit
      await this.auditService.logAsync(
        userId,
        'CHANGE_ROLE',
        'PTEIUser',
        userId,
        user.RoleId || '',
        roleId,
        'User role changed'
      );

      return true;
    } catch (error) {
      console.error('Error changing user role:', error);
      return false;
    }
  }

  /**
   * Unlocks a user account (equivalent to UnlockAccountAsync in C#)
   */
  async unlockAccountAsync(userId: string): Promise<boolean> {
    if (!DtoValidator.isValidUuid(userId)) {
      throw new Error('Invalid user ID format');
    }

    try {
      const user = await this.prisma.pTEIUser.findUnique({
        where: { UserId: userId }
      });

      if (!user) {
        return false;
      }

      await this.prisma.pTEIUser.update({
        where: { UserId: userId },
        data: {
          IsAccountLockedOut: false,
          FailedLoginAttempts: 0,
          UserModifiedAt: new Date()
        }
      });

      // Log audit
      await this.auditService.logAsync(
        userId,
        'UNLOCK_ACCOUNT',
        'PTEIUser',
        userId,
        'Locked',
        'Unlocked',
        'Account unlocked'
      );

      return true;
    } catch (error) {
      console.error('Error unlocking account:', error);
      return false;
    }
  }

  /**
   * Updates account status (equivalent to UpdateAccountStatusAsync in C#)
   */
  async updateAccountStatusAsync(changeStatusDto: ChangeStatusDto): Promise<boolean> {
    const validatedDto = ChangeStatusSchema.parse(changeStatusDto);

    try {
      const user = await this.prisma.pTEIUser.findUnique({
        where: { UserId: validatedDto.userId }
      });

      if (!user) {
        return false;
      }

      await this.prisma.pTEIUser.update({
        where: { UserId: validatedDto.userId },
        data: {
          AccountStatus: validatedDto.newStatus,
          UserModifiedAt: new Date()
        }
      });

      // Log audit
      await this.auditService.logAsync(
        validatedDto.userId,
        'STATUS_CHANGE',
        'PTEIUser',
        validatedDto.userId,
        user.AccountStatus,
        validatedDto.newStatus,
        `Status changed from ${user.AccountStatus} to ${validatedDto.newStatus}`
      );

      return true;
    } catch (error) {
      console.error('Error updating account status:', error);
      return false;
    }
  }

  /**
   * Verifies email (equivalent to VerifyEmailAsync in C#)
   */
  async verifyEmailAsync(verifyEmailDto: VerifyEmailDto): Promise<boolean> {
    const validatedDto = VerifyEmailSchema.parse(verifyEmailDto);

    try {
      const user = await this.prisma.pTEIUser.findUnique({
        where: { email: validatedDto.email }
      });

      if (!user || user.EmailVerificationToken !== validatedDto.token) {
        return false;
      }

      await this.prisma.pTEIUser.update({
        where: { email: validatedDto.email },
        data: {
          EmailVerificationToken: null,
          EmailVerificationTokenExpiry: null,
          AccountStatus: 'Active',
          UserModifiedAt: new Date()
        }
      });

      // Log audit
      await this.auditService.logAsync(
        user.UserId,
        'EMAIL_VERIFIED',
        'PTEIUser',
        user.UserId,
        'Pending',
        'Active',
        'Email verified and account activated'
      );

      return true;
    } catch (error) {
      console.error('Error verifying email:', error);
      return false;
    }
  }

  /**
   * Requests password reset (equivalent to RequestPasswordResetAsync in C#)
   */
  async requestPasswordResetAsync(email: string): Promise<boolean> {
    if (!DtoValidator.isValidEmail(email)) {
      throw new Error('Invalid email format');
    }

    try {
      const user = await this.prisma.pTEIUser.findUnique({
        where: { email: email }
      });

      if (!user) {
        return false;
      }

      const resetToken = crypto.randomUUID();
      const tokenExpiry = new Date();
      tokenExpiry.setMinutes(tokenExpiry.getMinutes() + 30); // 30 minutes from now

      await this.prisma.pTEIUser.update({
        where: { email: email },
        data: {
          PasswordResetToken: resetToken,
          PasswordResetTokenExpiry: tokenExpiry,
          UserModifiedAt: new Date()
        }
      });

      // Log audit
      await this.auditService.logAsync(
        user.UserId,
        'PASSWORD_RESET_REQUESTED',
        'PTEIUser',
        user.UserId,
        undefined,
        undefined,
        'Password reset requested'
      );

      return true;
    } catch (error) {
      console.error('Error requesting password reset:', error);
      return false;
    }
  }

  /**
   * Resets password (equivalent to ResetPasswordAsync in C#)
   */
  async resetPasswordAsync(resetPasswordDto: ResetPasswordDto): Promise<boolean> {
    const validatedDto = ResetPasswordSchema.parse(resetPasswordDto);

    try {
      const user = await this.prisma.pTEIUser.findUnique({
        where: { email: validatedDto.email }
      });

      if (!user ||
          user.PasswordResetToken !== validatedDto.token ||
          !user.PasswordResetTokenExpiry ||
          user.PasswordResetTokenExpiry < new Date()) {
        return false;
      }

      // Validate new password
      const passwordValidation = DtoValidator.validatePassword(validatedDto.newPassword);
      if (!passwordValidation.isValid) {
        throw new Error(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
      }

      const hashedPassword = this.passwordHasher.hash(validatedDto.newPassword);

      await this.prisma.pTEIUser.update({
        where: { email: validatedDto.email },
        data: {
          HashedPassword: hashedPassword,
          PasswordResetToken: null,
          PasswordResetTokenExpiry: null,
          UserModifiedAt: new Date()
        }
      });

      // Log audit
      await this.auditService.logAsync(
        user.UserId,
        'PASSWORD_RESET',
        'PTEIUser',
        user.UserId,
        undefined,
        undefined,
        'Password reset completed'
      );

      return true;
    } catch (error) {
      console.error('Error resetting password:', error);
      return false;
    }
  }

  /**
   * Gets profile picture path based on user type, role, and department
   * (equivalent to GetProfilePicPath in C#)
   */
  private getProfilePicPath(userType: string, roleId: string, departmentId?: string): string {
    if (userType === 'University') {
      return 'ProfilePics/PTEIUniversityUserPic.png';
    } else if (userType === 'TSLS') {
      // You might want to add more specific logic here based on department/role
      if (departmentId) {
        // This is a simplified check - you might want to look up the actual department name
        const departmentIdLower = departmentId.toLowerCase();
        if (departmentIdLower.includes('finance')) {
          return 'ProfilePics/PTEIFinanceUserPic.png';
        }
      }
      return 'ProfilePics/PTEIStudentServicesUserPic.png';
    }

    // Default for admin/super admin
    return 'ProfilePics/PTEIAdminUserPic.png';
  }

  /**
   * Creates a user and returns UserDto (wrapper for createUserAsync)
   */
  async createUser(createUserDto: CreateUserDto): Promise<UserDto> {
    const createdUser = await this.createUserAsync(createUserDto);

    // Get the full user with relations for DTO mapping
    const userWithRelations = await this.prisma.pTEIUser.findUnique({
      where: { UserId: createdUser.userId },
      include: {
        Role: true,
        Department: true
      }
    });

    if (!userWithRelations) {
      throw new Error('Failed to retrieve created user');
    }

    return this.mapToUserDto(userWithRelations);
  }

  /**
   * Maps Prisma user to UserDto
   */
  private mapToUserDto(prismaUser: any): UserDto {
    return {
      userId: prismaUser.UserId,
      email: prismaUser.email,
      firstName: prismaUser.FirstName,
      lastName: prismaUser.LastName,
      roleId: prismaUser.RoleId || '',
      roleName: prismaUser.Role?.RoleName || '',
      departmentId: prismaUser.DepartmentId,
      departmentName: prismaUser.Department?.DepartmentName || '',
      userType: prismaUser.UserType,
      accountStatus: prismaUser.AccountStatus,
      userCreatedAt: prismaUser.UserCreatedAt,
      userModifiedAt: prismaUser.UserModifiedAt || prismaUser.UserCreatedAt,
      isAccountLockedOut: prismaUser.IsAccountLockedOut,
      failedLoginAttempts: prismaUser.FailedLoginAttempts,
      userProfilePicPath: prismaUser.UserProfilePicPath || ''
    };
  }

  /**
   * Updates an existing user
   */
  async updateUser(updateUserDto: UpdateUserDto): Promise<UserDto> {
    // Validate DTO using Zod
    const validatedDto = UpdateUserSchema.parse(updateUserDto);

    // Get existing user
    const existingUser = await this.prisma.pTEIUser.findUnique({
      where: { UserId: validatedDto.userId },
      include: { Role: true, Department: true }
    });

    if (!existingUser) {
      throw new Error('User not found');
    }

    // Validate role if provided
    if (validatedDto.roleId) {
      const role = await this.prisma.role.findUnique({
        where: { RoleId: validatedDto.roleId }
      });

      if (!role) {
        throw new Error('Invalid role ID');
      }
    }

    // Validate department if provided
    if (validatedDto.departmentId) {
      const department = await this.prisma.department.findUnique({
        where: { DepartmentId: validatedDto.departmentId }
      });

      if (!department) {
        throw new Error('Invalid department ID');
      }
    }

    // Update user
    const updatedUser = await this.prisma.pTEIUser.update({
      where: { UserId: validatedDto.userId },
      data: {
        FirstName: validatedDto.firstName || existingUser.FirstName,
        LastName: validatedDto.lastName || existingUser.LastName,
        DepartmentId: validatedDto.departmentId || existingUser.DepartmentId,
        RoleId: validatedDto.roleId || existingUser.RoleId,
        UserModifiedAt: new Date()
      },
      include: {
        Role: true,
        Department: true
      }
    });

    // Log audit
    await this.auditService.logAsync(
      validatedDto.userId,
      'UPDATE',
      'PTEIUser',
      validatedDto.userId,
      JSON.stringify(existingUser),
      JSON.stringify(updatedUser),
      'User updated'
    );

    return this.mapToUserDto(updatedUser);
  }

  /**
   * Changes user status
   */
  async changeUserStatus(changeStatusDto: ChangeStatusDto): Promise<UserDto> {
    // Validate DTO using Zod
    const validatedDto = ChangeStatusSchema.parse(changeStatusDto);

    // Get existing user
    const existingUser = await this.prisma.pTEIUser.findUnique({
      where: { UserId: validatedDto.userId },
      include: { Role: true, Department: true }
    });

    if (!existingUser) {
      throw new Error('User not found');
    }

    // Update status
    const updatedUser = await this.prisma.pTEIUser.update({
      where: { UserId: validatedDto.userId },
      data: {
        AccountStatus: validatedDto.newStatus,
        UserModifiedAt: new Date()
      },
      include: {
        Role: true,
        Department: true
      }
    });

    // Log audit
    await this.auditService.logAsync(
      validatedDto.userId,
      'STATUS_CHANGE',
      'PTEIUser',
      validatedDto.userId,
      existingUser.AccountStatus,
      validatedDto.newStatus,
      `Status changed from ${existingUser.AccountStatus} to ${validatedDto.newStatus}`
    );

    return this.mapToUserDto(updatedUser);
  }

  /**
   * Unlocks a user account
   */
  async unlockAccount(unlockAccountDto: UnlockAccountDto): Promise<UserDto> {
    // Validate DTO
    const validatedDto = DtoValidator.validate(unlockAccountDto, UnlockAccountDtoSchema);

    // Get existing user
    const existingUser = await this.prisma.pTEIUser.findUnique({
      where: { UserId: validatedDto.userId },
      include: { Role: true, Department: true }
    });

    if (!existingUser) {
      throw new Error('User not found');
    }

    // Unlock account
    const updatedUser = await this.prisma.pTEIUser.update({
      where: { UserId: validatedDto.userId },
      data: {
        IsAccountLockedOut: false,
        FailedLoginAttempts: 0,
        UserModifiedAt: new Date()
      },
      include: {
        Role: true,
        Department: true
      }
    });

    // Log audit
    await this.auditService.logAsync(
      validatedDto.userId,
      'UNLOCK_ACCOUNT',
      'PTEIUser',
      validatedDto.userId,
      'Locked',
      'Unlocked',
      'Account unlocked'
    );

    return this.mapToUserDto(updatedUser);
  }


}
